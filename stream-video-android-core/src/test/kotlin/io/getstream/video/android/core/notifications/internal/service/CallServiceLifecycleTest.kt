/*
 * Copyright (c) 2014-2024 Stream.io Inc. All rights reserved.
 *
 * Licensed under the Stream License;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    https://github.com/GetStream/stream-video-android/blob/main/LICENSE
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.getstream.video.android.core.notifications.internal.service

import android.app.Service
import android.content.Intent
import io.getstream.video.android.core.StreamVideo
import io.getstream.video.android.core.StreamVideoClient
import io.getstream.video.android.core.notifications.internal.service.CallService.Companion.INTENT_EXTRA_CALL_CID
import io.getstream.video.android.core.notifications.internal.service.CallService.Companion.TRIGGER_INCOMING_CALL
import io.getstream.video.android.core.notifications.internal.service.CallService.Companion.TRIGGER_KEY
import io.getstream.video.android.core.notifications.internal.service.CallService.Companion.TRIGGER_ONGOING_CALL
import io.getstream.video.android.core.notifications.internal.service.CallService.Companion.TRIGGER_OUTGOING_CALL
import io.getstream.video.android.core.notifications.internal.service.CallService.Companion.TRIGGER_REMOVE_INCOMING_CALL
import io.getstream.video.android.model.StreamCallId
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.unmockkObject
import org.junit.After
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.Robolectric
import org.robolectric.RobolectricTestRunner
import org.robolectric.android.controller.ServiceController

/**
 * Tests for CallService lifecycle and behavior that require Robolectric
 * for proper Android Service testing.
 */
@RunWith(RobolectricTestRunner::class)
class CallServiceLifecycleTest {

    @MockK
    lateinit var mockStreamVideoClient: StreamVideoClient

    @MockK
    lateinit var mockClientState: io.getstream.video.android.core.ClientState

    @MockK
    lateinit var mockCall: io.getstream.video.android.core.Call

    private lateinit var serviceController: ServiceController<CallService>
    private lateinit var service: CallService
    private lateinit var testCallId: StreamCallId

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxUnitFun = true)
        
        // Mock StreamVideo singleton
        mockkObject(StreamVideo)
        every { StreamVideo.instanceOrNull() } returns mockStreamVideoClient
        
        testCallId = StreamCallId(type = "default", id = "test-call-123")
        
        // Setup basic mocks
        every { mockStreamVideoClient.state } returns mockClientState
        every { mockClientState.activeCall } returns mockk {
            every { value } returns null
        }
        every { mockStreamVideoClient.call(any(), any()) } returns mockCall
        every { mockStreamVideoClient.permissionCheck } returns mockk {
            every { checkAndroidPermissions(any(), any()) } returns true
        }
        every { mockStreamVideoClient.crashOnMissingPermission } returns false
        every { mockStreamVideoClient.getOngoingCallNotification(any(), any()) } returns null
        every { mockStreamVideoClient.getRingingCallNotification(any(), any(), any(), any()) } returns null
        every { mockStreamVideoClient.getSettingUpCallNotification() } returns null
        every { mockStreamVideoClient.connectIfNotAlreadyConnected() } returns Unit
        every { mockCall.get() } returns mockk {
            every { isSuccess } returns true
        }

        // Create service controller
        serviceController = Robolectric.buildService(CallService::class.java)
        service = serviceController.create().get()
    }

    @After
    fun tearDown() {
        unmockkObject(StreamVideo)
        if (::serviceController.isInitialized) {
            serviceController.destroy()
        }
    }

    @Test
    fun `service returns START_REDELIVER_INTENT when required parameters are missing`() {
        // Given - Intent without required extras
        val intent = Intent()

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_REDELIVER_INTENT, result)
    }

    @Test
    fun `service returns START_REDELIVER_INTENT when StreamVideo instance is null`() {
        // Given
        every { StreamVideo.instanceOrNull() } returns null
        val intent = createValidIntent(TRIGGER_INCOMING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_REDELIVER_INTENT, result)
    }

    @Test
    fun `service returns START_NOT_STICKY when successfully started with valid parameters`() {
        // Given
        val intent = createValidIntent(TRIGGER_ONGOING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_NOT_STICKY, result)
    }

    @Test
    fun `service handles incoming call trigger correctly`() {
        // Given
        val intent = createValidIntent(TRIGGER_INCOMING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_NOT_STICKY, result)
        // Additional verification could be added here for specific incoming call behavior
    }

    @Test
    fun `service handles outgoing call trigger correctly`() {
        // Given
        val intent = createValidIntent(TRIGGER_OUTGOING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_NOT_STICKY, result)
    }

    @Test
    fun `service handles ongoing call trigger correctly`() {
        // Given
        val intent = createValidIntent(TRIGGER_ONGOING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_NOT_STICKY, result)
    }

    @Test
    fun `service handles remove incoming call trigger correctly`() {
        // Given
        val intent = createValidIntent(TRIGGER_REMOVE_INCOMING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_NOT_STICKY, result)
    }

    @Test
    fun `service returns START_REDELIVER_INTENT when permission check fails`() {
        // Given
        every { mockStreamVideoClient.permissionCheck.checkAndroidPermissions(any(), any()) } returns false
        every { mockStreamVideoClient.crashOnMissingPermission } returns false
        val intent = createValidIntent(TRIGGER_INCOMING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_REDELIVER_INTENT, result)
    }

    @Test
    fun `service returns START_REDELIVER_INTENT when notification generation fails`() {
        // Given
        // Mock all notification methods to return null (simulating failure)
        every { mockStreamVideoClient.getOngoingCallNotification(any(), any()) } returns null
        every { mockStreamVideoClient.getRingingCallNotification(any(), any(), any(), any()) } returns null
        val intent = createValidIntent(TRIGGER_ONGOING_CALL)

        // When
        val result = service.onStartCommand(intent, 0, 1)

        // Then
        assertEquals(Service.START_REDELIVER_INTENT, result)
    }

    @Test
    fun `service handles null intent gracefully`() {
        // When
        val result = service.onStartCommand(null, 0, 1)

        // Then
        assertEquals(Service.START_REDELIVER_INTENT, result)
    }

    @Test
    fun `service can be bound and unbound`() {
        // When
        val binder = service.onBind(Intent())

        // Then - CallService returns null for onBind (it's a started service, not bound)
        assertEquals(null, binder)
    }

    @Test
    fun `service handles multiple start commands`() {
        // Given
        val intent1 = createValidIntent(TRIGGER_INCOMING_CALL)
        val intent2 = createValidIntent(TRIGGER_ONGOING_CALL)

        // When
        val result1 = service.onStartCommand(intent1, 0, 1)
        val result2 = service.onStartCommand(intent2, 0, 2)

        // Then
        assertEquals(Service.START_NOT_STICKY, result1)
        assertEquals(Service.START_NOT_STICKY, result2)
    }

    private fun createValidIntent(trigger: String): Intent {
        return Intent().apply {
            putExtra(INTENT_EXTRA_CALL_CID, testCallId)
            putExtra(TRIGGER_KEY, trigger)
            if (trigger == TRIGGER_INCOMING_CALL) {
                putExtra(CallService.INTENT_EXTRA_CALL_DISPLAY_NAME, "Test User")
            }
        }
    }

    // Test service destruction and cleanup
    @Test
    fun `service can be destroyed without errors`() {
        // Given
        val intent = createValidIntent(TRIGGER_ONGOING_CALL)
        service.onStartCommand(intent, 0, 1)

        // When - Destroy the service
        serviceController.destroy()

        // Then - Should complete without throwing exceptions
        // The test passes if no exceptions are thrown
    }

    // Test service with different configurations
    @Test
    fun `service works with different service types`() {
        // Test that different service subclasses can be instantiated
        val livestreamService = LivestreamCallService()
        val audioService = LivestreamAudioCallService()
        val viewerService = LivestreamViewerService()
        val audioCallService = AudioCallService()

        // Verify they all extend CallService and have different service types
        assert(livestreamService is CallService)
        assert(audioService is CallService)
        assert(viewerService is CallService)
        assert(audioCallService is CallService)

        // Verify they have different service types
        assert(livestreamService.serviceType != audioService.serviceType)
        assert(audioService.serviceType != viewerService.serviceType)
        assert(viewerService.serviceType != audioCallService.serviceType)
    }
}
