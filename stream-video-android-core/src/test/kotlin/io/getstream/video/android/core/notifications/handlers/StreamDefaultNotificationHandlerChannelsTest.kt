/*
 * Copyright (c) 2014-2024 Stream.io Inc. All rights reserved.
 *
 * Licensed under the Stream License;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    https://github.com/GetStream/stream-video-android/blob/main/LICENSE
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package io.getstream.video.android.core.notifications.handlers

import android.app.Application
import android.app.NotificationManager
import io.getstream.android.push.permissions.DefaultNotificationPermissionHandler
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment

@RunWith(RobolectricTestRunner::class)
class StreamDefaultNotificationHandlerChannelsTest {

    @MockK
    lateinit var mockApplication: Application

    private lateinit var application: Application
    private lateinit var notificationHandler: StreamDefaultNotificationHandler

    @Before
    fun setUp() {
        MockKAnnotations.init(this, relaxUnitFun = true)
        application = RuntimeEnvironment.getApplication()
        
        // Mock application context to return the real application for string resources
        every { mockApplication.applicationContext } returns application
        
        notificationHandler = StreamDefaultNotificationHandler(
            application = mockApplication,
            notificationPermissionHandler = DefaultNotificationPermissionHandler.createDefaultNotificationPermissionHandler(mockApplication),
            hideRingingNotificationInForeground = false
        )
    }

    @Test
    fun `default notification handler has correct incoming call channel configuration`() {
        // When
        val incomingChannel = notificationHandler.notificationChannels.incomingCallChannel

        // Then
        assertEquals("incoming_calls", incomingChannel.id)
        assertEquals("Incoming Calls", incomingChannel.name)
        assertEquals("Incoming audio and video call alerts", incomingChannel.description)
        assertEquals(NotificationManager.IMPORTANCE_HIGH, incomingChannel.importance)
    }

    @Test
    fun `default notification handler has correct ongoing call channel configuration`() {
        // When
        val ongoingChannel = notificationHandler.notificationChannels.ongoingCallChannel

        // Then
        assertEquals("ongoing_calls", ongoingChannel.id)
        assertEquals("Ongoing Calls", ongoingChannel.name)
        assertEquals("Ongoing call notifications", ongoingChannel.description)
        assertEquals(NotificationManager.IMPORTANCE_DEFAULT, ongoingChannel.importance)
    }

    @Test
    fun `default notification handler has correct outgoing call channel configuration`() {
        // When
        val outgoingChannel = notificationHandler.notificationChannels.outgoingCallChannel

        // Then
        assertEquals("outgoing_calls", outgoingChannel.id)
        assertEquals("Outgoing Calls", outgoingChannel.name)
        assertEquals("Outgoing call notifications", outgoingChannel.description)
        assertEquals(NotificationManager.IMPORTANCE_DEFAULT, outgoingChannel.importance)
    }

    @Test
    fun `default notification handler has correct missed call channel configuration`() {
        // When
        val missedChannel = notificationHandler.notificationChannels.missedCallChannel

        // Then
        assertEquals("stream_missed_call_channel", missedChannel.id)
        assertEquals("Missed Calls", missedChannel.name)
        assertEquals("Missed call notifications", missedChannel.description)
        assertEquals(NotificationManager.IMPORTANCE_HIGH, missedChannel.importance)
    }

    @Test
    fun `default notification handler channels have correct importance levels`() {
        // When
        val channels = notificationHandler.notificationChannels

        // Then - High importance channels (should interrupt user)
        assertEquals(NotificationManager.IMPORTANCE_HIGH, channels.incomingCallChannel.importance)
        assertEquals(NotificationManager.IMPORTANCE_HIGH, channels.missedCallChannel.importance)
        
        // Default importance channels (less intrusive)
        assertEquals(NotificationManager.IMPORTANCE_DEFAULT, channels.ongoingCallChannel.importance)
        assertEquals(NotificationManager.IMPORTANCE_DEFAULT, channels.outgoingCallChannel.importance)
    }

    @Test
    fun `compatibility notification handler has same channel configuration as default handler`() {
        // Given
        val compatibilityHandler = CompatibilityStreamNotificationHandler(
            application = mockApplication,
            hideRingingNotificationInForeground = false
        )

        // When
        val defaultChannels = notificationHandler.notificationChannels
        val compatibilityChannels = compatibilityHandler.notificationChannels

        // Then - All channels should be identical
        assertEquals(defaultChannels.incomingCallChannel.id, compatibilityChannels.incomingCallChannel.id)
        assertEquals(defaultChannels.incomingCallChannel.name, compatibilityChannels.incomingCallChannel.name)
        assertEquals(defaultChannels.incomingCallChannel.description, compatibilityChannels.incomingCallChannel.description)
        assertEquals(defaultChannels.incomingCallChannel.importance, compatibilityChannels.incomingCallChannel.importance)

        assertEquals(defaultChannels.ongoingCallChannel.id, compatibilityChannels.ongoingCallChannel.id)
        assertEquals(defaultChannels.ongoingCallChannel.name, compatibilityChannels.ongoingCallChannel.name)
        assertEquals(defaultChannels.ongoingCallChannel.description, compatibilityChannels.ongoingCallChannel.description)
        assertEquals(defaultChannels.ongoingCallChannel.importance, compatibilityChannels.ongoingCallChannel.importance)

        assertEquals(defaultChannels.outgoingCallChannel.id, compatibilityChannels.outgoingCallChannel.id)
        assertEquals(defaultChannels.outgoingCallChannel.name, compatibilityChannels.outgoingCallChannel.name)
        assertEquals(defaultChannels.outgoingCallChannel.description, compatibilityChannels.outgoingCallChannel.description)
        assertEquals(defaultChannels.outgoingCallChannel.importance, compatibilityChannels.outgoingCallChannel.importance)

        assertEquals(defaultChannels.missedCallChannel.id, compatibilityChannels.missedCallChannel.id)
        assertEquals(defaultChannels.missedCallChannel.name, compatibilityChannels.missedCallChannel.name)
        assertEquals(defaultChannels.missedCallChannel.description, compatibilityChannels.missedCallChannel.description)
        assertEquals(defaultChannels.missedCallChannel.importance, compatibilityChannels.missedCallChannel.importance)
    }

    @Test
    fun `notification handler can be created with custom channels`() {
        // Given
        val customIncomingChannel = StreamNotificationChannelInfo(
            id = "custom_incoming",
            name = "Custom Incoming",
            description = "Custom incoming description",
            importance = NotificationManager.IMPORTANCE_LOW
        )
        val customOngoingChannel = StreamNotificationChannelInfo(
            id = "custom_ongoing",
            name = "Custom Ongoing",
            description = "Custom ongoing description",
            importance = NotificationManager.IMPORTANCE_MIN
        )
        val customOutgoingChannel = StreamNotificationChannelInfo(
            id = "custom_outgoing",
            name = "Custom Outgoing",
            description = "Custom outgoing description",
            importance = NotificationManager.IMPORTANCE_MAX
        )
        val customMissedChannel = StreamNotificationChannelInfo(
            id = "custom_missed",
            name = "Custom Missed",
            description = "Custom missed description",
            importance = NotificationManager.IMPORTANCE_LOW
        )

        val customChannels = StreamNotificationChannels(
            incomingCallChannel = customIncomingChannel,
            ongoingCallChannel = customOngoingChannel,
            outgoingCallChannel = customOutgoingChannel,
            missedCallChannel = customMissedChannel
        )

        // When
        val customHandler = StreamDefaultNotificationHandler(
            application = mockApplication,
            notificationPermissionHandler = DefaultNotificationPermissionHandler.createDefaultNotificationPermissionHandler(mockApplication),
            hideRingingNotificationInForeground = false,
            notificationChannels = customChannels
        )

        // Then
        assertEquals(customIncomingChannel, customHandler.notificationChannels.incomingCallChannel)
        assertEquals(customOngoingChannel, customHandler.notificationChannels.ongoingCallChannel)
        assertEquals(customOutgoingChannel, customHandler.notificationChannels.outgoingCallChannel)
        assertEquals(customMissedChannel, customHandler.notificationChannels.missedCallChannel)
    }

    @Test
    fun `channel IDs are consistent with string resource values`() {
        // This test ensures that if someone changes the string resources,
        // the test will fail and alert them to the breaking change
        
        // When
        val channels = notificationHandler.notificationChannels

        // Then - These values should match the string resources exactly
        assertEquals("incoming_calls", channels.incomingCallChannel.id)
        assertEquals("ongoing_calls", channels.ongoingCallChannel.id)
        assertEquals("outgoing_calls", channels.outgoingCallChannel.id)
        assertEquals("stream_missed_call_channel", channels.missedCallChannel.id)
    }

    @Test
    fun `channel names are consistent with string resource values`() {
        // This test ensures that if someone changes the string resources,
        // the test will fail and alert them to the breaking change
        
        // When
        val channels = notificationHandler.notificationChannels

        // Then - These values should match the string resources exactly
        assertEquals("Incoming Calls", channels.incomingCallChannel.name)
        assertEquals("Ongoing Calls", channels.ongoingCallChannel.name)
        assertEquals("Outgoing Calls", channels.outgoingCallChannel.name)
        assertEquals("Missed Calls", channels.missedCallChannel.name)
    }

    @Test
    fun `channel descriptions are consistent with string resource values`() {
        // This test ensures that if someone changes the string resources,
        // the test will fail and alert them to the breaking change
        
        // When
        val channels = notificationHandler.notificationChannels

        // Then - These values should match the string resources exactly
        assertEquals("Incoming audio and video call alerts", channels.incomingCallChannel.description)
        assertEquals("Ongoing call notifications", channels.ongoingCallChannel.description)
        assertEquals("Outgoing call notifications", channels.outgoingCallChannel.description)
        assertEquals("Missed call notifications", channels.missedCallChannel.description)
    }
}
